[project]
name = "django-survey"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
classifiers = [
  "Programming Language :: Python :: 3 :: Only",
  "Programming Language :: Python :: 3.12",
]
dependencies = [
  "crispy-tailwind>=1.0.3",
  "django>=5.2.2",
  "django-crispy-forms>=2.4",
  "django-tailwind[reload]>=4.0.1",
  "python-decouple>=3.8",
  "whitenoise>=6.9",
]

[tool.ruff.format]
quote-style = "single"

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "config.settings"
addopts = "--cov=. --cov-report=term"

[tool.coverage.run]
source = ["."]
omit = [
    "*/__init__.py",
    "*/tests/*",
    "*/migrations/*",
    "manage.py",
    "config/settings.py",
    "config/wsgi.py",
    "config/asgi.py",
]

[dependency-groups]
dev = [
    "django-debug-toolbar>=5.2.0",
    "pre-commit>=4.2.0",
]
