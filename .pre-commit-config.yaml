repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: debug-statements
      - id: check-merge-conflict

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.3.4
    hooks:
      - id: ruff
        args: [--fix]
      - id: ruff-format

  - repo: https://github.com/tox-dev/pyproject-fmt
    rev: "1.7.0"
    hooks:
      - id: pyproject-fmt

  - repo: https://github.com/asottile/pyupgrade
    rev: v3.19.1
    hooks:
      - id: pyupgrade
        args:
          - '--py311-plus'

  - repo: https://github.com/adamchainz/django-upgrade
    rev: 1.14.0
    hooks:
      - id: django-upgrade
        args: [--target-version, '4.2']

  - repo: https://github.com/djlint/djLint
    rev: v1.36.4
    hooks:
      - id: djlint-reformat-django
        args: [--indent=2, --preserve-blank-lines]
