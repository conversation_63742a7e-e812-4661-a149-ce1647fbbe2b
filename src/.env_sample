# Django settings
DJANGO_SECRET_KEY='your-django-secret-key'
DEBUG=True
ALLOWED_HOSTS='.127.0.0.1, localhost'

# Security settings (for production)
# Set to False in development, True in production with HTTPS
SECURE_SSL_REDIRECT=False

# Email Configuration
# for development, you can use MailHog.
EMAIL_BACKEND='django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST='127.0.0.1'
EMAIL_PORT=8025
EMAIL_USE_TLS=False
EMAIL_HOST_USER=''
EMAIL_HOST_PASSWORD=''
DEFAULT_FROM_EMAIL='<EMAIL>'

# Database settings (SQLite by default)
# For PostgreSQL, uncomment and set these values
# DB_ENGINE=django.db.backends.postgresql
# DB_NAME=postgres
# DB_USER=postgres
# DB_PASSWORD=postgres
# DB_HOST=db
# DB_PORT=5432

# NPM settings (for Tailwind CSS)
# For Windows use 'npm.cmd', for Linux/Mac use 'npm'
NPM_BIN_PATH='npm.cmd'
