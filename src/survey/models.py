from django.contrib.auth import get_user_model
from django.db import models

User = get_user_model()


class Survey(models.Model):
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('open', 'Open'),
        ('closed', 'Closed'),
    ]

    title = models.CharField(max_length=255)
    owner = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name='owned_surveys'
    )
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='draft')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = 'surveys'
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    @property
    def is_open(self):
        return self.status == 'open'

    @property
    def is_closed(self):
        return self.status == 'closed'

    def can_be_taken_by(self, user):
        """Check if a user can take this survey"""
        if not self.is_open:
            return False
        # Check if user has already responded (bonus feature)
        if user.is_authenticated:
            return not self.responses.filter(respondent=user).exists()
        return True


class Question(models.Model):
    survey = models.ForeignKey(
        Survey, on_delete=models.CASCADE, related_name='questions'
    )
    title = models.CharField(max_length=255)
    order = models.PositiveIntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order']
        unique_together = ['survey', 'order']

    def __str__(self):
        return f'{self.survey.title} - Q{self.order}: {self.title}'


class Choice(models.Model):
    question = models.ForeignKey(
        Question, on_delete=models.CASCADE, related_name='choices'
    )
    text = models.CharField(max_length=255)
    order = models.PositiveIntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order']
        unique_together = ['question', 'order']

    def __str__(self):
        return f'{self.question.title} - {self.text}'


class SurveyResponse(models.Model):
    survey = models.ForeignKey(
        Survey, on_delete=models.CASCADE, related_name='responses'
    )
    respondent = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='survey_responses',
    )
    session_key = models.CharField(
        max_length=40, null=True, blank=True
    )  # For anonymous users
    submitted_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        # Ensure one response per user per survey (bonus feature)
        unique_together = ['survey', 'respondent']

    def __str__(self):
        if self.respondent:
            return f'{self.survey.title} - {self.respondent.username}'
        return f'{self.survey.title} - Anonymous'


class QuestionResponse(models.Model):
    survey_response = models.ForeignKey(
        SurveyResponse, on_delete=models.CASCADE, related_name='question_responses'
    )
    question = models.ForeignKey(Question, on_delete=models.CASCADE)
    selected_choice = models.ForeignKey(Choice, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['survey_response', 'question']

    def __str__(self):
        return f'{self.survey_response} - {self.question.title}: {self.selected_choice.text}'
