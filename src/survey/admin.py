from django.contrib import admin

from .models import Choice, Question, QuestionResponse, Survey, SurveyResponse


class QuestionInline(admin.TabularInline):
    model = Question
    extra = 1
    fields = ('title', 'order')


class ChoiceInline(admin.TabularInline):
    model = Choice
    extra = 1
    fields = ('text', 'order')


@admin.register(Survey)
class SurveyAdmin(admin.ModelAdmin):
    list_display = ('title', 'owner', 'status', 'created_at', 'updated_at')
    list_filter = ('status', 'created_at', 'owner')
    search_fields = ('title', 'owner__username')
    inlines = [QuestionInline]
    readonly_fields = ('created_at', 'updated_at')


@admin.register(Question)
class QuestionAdmin(admin.ModelAdmin):
    list_display = ('title', 'survey', 'order', 'created_at')
    list_filter = ('survey', 'created_at')
    search_fields = ('title', 'survey__title')
    inlines = [ChoiceInline]
    readonly_fields = ('created_at', 'updated_at')


@admin.register(Choice)
class ChoiceAdmin(admin.ModelAdmin):
    list_display = ('text', 'question', 'order', 'created_at')
    list_filter = ('question__survey', 'created_at')
    search_fields = ('text', 'question__title')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(SurveyResponse)
class SurveyResponseAdmin(admin.ModelAdmin):
    list_display = ('survey', 'respondent', 'submitted_at')
    list_filter = ('survey', 'submitted_at')
    search_fields = ('survey__title', 'respondent__username')
    readonly_fields = ('submitted_at',)


@admin.register(QuestionResponse)
class QuestionResponseAdmin(admin.ModelAdmin):
    list_display = ('survey_response', 'question', 'selected_choice', 'created_at')
    list_filter = ('question__survey', 'created_at')
    search_fields = ('question__title', 'selected_choice__text')
    readonly_fields = ('created_at',)
